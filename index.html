<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Torre Infinita - RPG por Turnos</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto+Mono:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div id="game-container">
        <!-- Header con información del jugador -->
        <header id="player-header">
            <div class="player-info">
                <h1 class="game-title">🗼 TORRE INFINITA</h1>
                <div class="player-stats">
                    <div class="stat-group">
                        <span class="stat-label">Nivel:</span>
                        <span id="player-level" class="stat-value">1</span>
                    </div>
                    <div class="stat-group">
                        <span class="stat-label">Piso:</span>
                        <span id="current-floor" class="stat-value">1</span>
                    </div>
                    <div class="stat-group">
                        <span class="stat-label">Oro:</span>
                        <span id="player-gold" class="stat-value">0</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Panel principal del juego -->
        <main id="game-main">
            <!-- Panel izquierdo: Stats del jugador -->
            <section id="player-panel" class="game-panel">
                <h2>Tu Aventurero</h2>
                <div class="player-avatar">⚔️</div>
                <div class="player-name" id="player-name">Héroe</div>
                
                <div class="stats-container">
                    <div class="stat-bar">
                        <label>HP</label>
                        <div class="bar-container">
                            <div class="bar hp-bar">
                                <div id="hp-fill" class="bar-fill"></div>
                            </div>
                            <span id="hp-text" class="bar-text">100/100</span>
                        </div>
                    </div>
                    
                    <div class="stat-bar">
                        <label>MP</label>
                        <div class="bar-container">
                            <div class="bar mp-bar">
                                <div id="mp-fill" class="bar-fill"></div>
                            </div>
                            <span id="mp-text" class="bar-text">50/50</span>
                        </div>
                    </div>
                    
                    <div class="stat-bar">
                        <label>EXP</label>
                        <div class="bar-container">
                            <div class="bar exp-bar">
                                <div id="exp-fill" class="bar-fill"></div>
                            </div>
                            <span id="exp-text" class="bar-text">0/100</span>
                        </div>
                    </div>
                </div>

                <div class="detailed-stats">
                    <div class="stat-row">
                        <span>ATK:</span>
                        <span id="player-atk">10</span>
                    </div>
                    <div class="stat-row">
                        <span>DEF:</span>
                        <span id="player-def">5</span>
                    </div>
                    <div class="stat-row">
                        <span>SPD:</span>
                        <span id="player-spd">8</span>
                    </div>
                </div>
            </section>

            <!-- Panel central: Combate y narrativa -->
            <section id="combat-panel" class="game-panel main-panel">
                <div id="combat-area">
                    <div id="enemy-section" class="combatant-section">
                        <div class="enemy-avatar" id="enemy-avatar">👹</div>
                        <div class="enemy-name" id="enemy-name">Goblin Salvaje</div>
                        <div class="enemy-hp-bar">
                            <div class="bar hp-bar">
                                <div id="enemy-hp-fill" class="bar-fill"></div>
                            </div>
                            <span id="enemy-hp-text" class="bar-text">80/80</span>
                        </div>
                    </div>

                    <div id="combat-log" class="combat-log">
                        <div class="log-entry welcome">
                            ¡Bienvenido a la Torre Infinita! 🗼<br>
                            Te encuentras en el primer piso. Un Goblin Salvaje bloquea tu camino...
                        </div>
                    </div>

                    <div id="action-buttons" class="action-buttons">
                        <button id="attack-btn" class="action-btn primary">⚔️ Atacar</button>
                        <button id="defend-btn" class="action-btn secondary">🛡️ Defender</button>
                        <button id="skill-btn" class="action-btn magic">✨ Habilidad</button>
                        <button id="flee-btn" class="action-btn danger">🏃 Huir</button>
                    </div>
                </div>

                <div id="exploration-area" class="hidden">
                    <div class="floor-info">
                        <h3>Piso <span id="floor-number">1</span></h3>
                        <p id="floor-description">Un pasillo oscuro se extiende ante ti...</p>
                    </div>
                    <div class="exploration-buttons">
                        <button id="next-floor-btn" class="action-btn primary">🔼 Subir al siguiente piso</button>
                        <button id="rest-btn" class="action-btn secondary">😴 Descansar (Recuperar HP/MP)</button>
                        <button id="shop-btn" class="action-btn magic">🏪 Tienda</button>
                    </div>
                </div>
            </section>

            <!-- Panel derecho: Inventario y equipamiento -->
            <section id="inventory-panel" class="game-panel">
                <h2>Inventario</h2>
                
                <div class="equipment-slots">
                    <h3>Equipamiento</h3>
                    <div class="equipment-grid">
                        <div class="equipment-slot" data-slot="weapon">
                            <div class="slot-icon">⚔️</div>
                            <div class="slot-name">Arma</div>
                            <div id="equipped-weapon" class="equipped-item">Espada de Hierro</div>
                        </div>
                        <div class="equipment-slot" data-slot="armor">
                            <div class="slot-icon">🛡️</div>
                            <div class="slot-name">Armadura</div>
                            <div id="equipped-armor" class="equipped-item">Armadura de Cuero</div>
                        </div>
                    </div>
                </div>

                <div class="inventory-items">
                    <h3>Objetos</h3>
                    <div id="inventory-list" class="item-list">
                        <div class="item" data-item="potion">
                            <span class="item-icon">🧪</span>
                            <span class="item-name">Poción de Vida</span>
                            <span class="item-count">x3</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer con controles adicionales -->
        <footer id="game-footer">
            <div class="footer-controls">
                <button id="save-btn" class="control-btn">💾 Guardar</button>
                <button id="load-btn" class="control-btn">📁 Cargar</button>
                <button id="settings-btn" class="control-btn">⚙️ Configuración</button>
            </div>
        </footer>
    </div>

    <!-- Modales -->
    <div id="level-up-modal" class="modal hidden">
        <div class="modal-content">
            <h2>¡NIVEL SUBIDO! 🎉</h2>
            <p>Has alcanzado el nivel <span id="new-level"></span></p>
            <p>Tienes <span id="stat-points"></span> puntos para distribuir:</p>
            <div class="stat-distribution">
                <div class="stat-option">
                    <span>ATK:</span>
                    <button class="stat-btn" data-stat="atk">+</button>
                    <span id="atk-points">0</span>
                </div>
                <div class="stat-option">
                    <span>DEF:</span>
                    <button class="stat-btn" data-stat="def">+</button>
                    <span id="def-points">0</span>
                </div>
                <div class="stat-option">
                    <span>SPD:</span>
                    <button class="stat-btn" data-stat="spd">+</button>
                    <span id="spd-points">0</span>
                </div>
            </div>
            <button id="confirm-stats" class="action-btn primary">Confirmar</button>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
