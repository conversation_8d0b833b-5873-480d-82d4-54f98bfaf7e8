// Torre Infinita - RPG por Turnos
// Sistema de juego principal

class Player {
    constructor() {
        this.level = 1;
        this.exp = 0;
        this.expToNext = 100;

        // Stats base
        this.maxHp = 100;
        this.hp = 100;
        this.maxMp = 50;
        this.mp = 50;
        this.atk = 10;
        this.def = 5;
        this.spd = 8;

        // Progreso
        this.currentFloor = 1;
        this.gold = 0;

        // Estado
        this.defending = false;

        // Sistema de habilidades
        this.skills = {
            magicStrike: { name: "<PERSON><PERSON><PERSON>", mpCost: 10, cooldown: 0, maxCooldown: 0 },
            heal: { name: "Cura<PERSON>", mpCost: 15, cooldown: 0, maxCooldown: 3 },
            fireball: { name: "<PERSON><PERSON>", mpCost: 20, cooldown: 0, maxCooldown: 2, unlocked: false },
            shield: { name: "<PERSON><PERSON><PERSON><PERSON>", mpCost: 12, cooldown: 0, maxCooldown: 4, unlocked: false }
        };

        this.currentSkill = 'magicStrike';

        // Sistema de logros
        this.achievements = {
            enemiesKilled: 0,
            floorsCleared: 0,
            bossesKilled: 0,
            itemsFound: 0,
            goldEarned: 0
        };

        // Inventario
        this.inventory = {
            potion: 3
        };

        // Equipamiento
        this.equipment = {
            weapon: { name: "Espada de Hierro", atk: 5 },
            armor: { name: "Armadura de Cuero", def: 3 }
        };
    }

    takeDamage(damage) {
        this.hp = Math.max(0, this.hp - damage);
    }

    heal(amount) {
        this.hp = Math.min(this.maxHp, this.hp + amount);
    }

    restoreMp(amount) {
        this.mp = Math.min(this.maxMp, this.mp + amount);
    }

    gainExp(amount) {
        this.exp += amount;

        while (this.exp >= this.expToNext) {
            this.levelUp();
        }
    }

    levelUp() {
        this.exp -= this.expToNext;
        this.level++;

        // Incrementar stats base
        this.maxHp += 20;
        this.hp = this.maxHp; // Curación completa al subir nivel
        this.maxMp += 10;
        this.mp = this.maxMp;
        this.atk += 2;
        this.def += 1;
        this.spd += 1;

        // Desbloquear habilidades según el nivel
        this.unlockSkills();

        // Calcular nueva experiencia necesaria
        this.expToNext = Math.floor(this.expToNext * 1.5);

        // Mostrar modal de subida de nivel (implementar después)
        this.showLevelUpModal();
    }

    unlockSkills() {
        if (this.level >= 5 && !this.skills.fireball.unlocked) {
            this.skills.fireball.unlocked = true;
            if (window.game) {
                window.game.addToLog("🔥 ¡Nueva habilidad desbloqueada: Bola de Fuego!", "heal");
            }
        }
        if (this.level >= 3 && !this.skills.heal.unlocked) {
            this.skills.heal.unlocked = true;
            if (window.game) {
                window.game.addToLog("💚 ¡Nueva habilidad desbloqueada: Curación!", "heal");
            }
        }
        if (this.level >= 7 && !this.skills.shield.unlocked) {
            this.skills.shield.unlocked = true;
            if (window.game) {
                window.game.addToLog("🛡️ ¡Nueva habilidad desbloqueada: Escudo Mágico!", "heal");
            }
        }
    }

    reduceSkillCooldowns() {
        for (const skill of Object.values(this.skills)) {
            if (skill.cooldown > 0) {
                skill.cooldown--;
            }
        }
    }

    showLevelUpModal() {
        if (window.game) {
            window.game.addToLog(`🎉 ¡NIVEL SUBIDO! Ahora eres nivel ${this.level}!`, "heal");
            window.game.addToLog("✨ Todas tus estadísticas han mejorado!", "heal");
            window.game.addToLog(`💪 Tienes 3 puntos para distribuir!`, "info");

            // Mostrar modal de distribución de puntos
            window.game.showStatDistributionModal();
        }
    }

    addItem(itemType, quantity) {
        if (this.inventory[itemType]) {
            this.inventory[itemType] += quantity;
        } else {
            this.inventory[itemType] = quantity;
        }
    }

    useItem(itemType) {
        if (this.inventory[itemType] && this.inventory[itemType] > 0) {
            this.inventory[itemType]--;

            switch(itemType) {
                case 'potion':
                    const healAmount = 50;
                    this.heal(healAmount);
                    if (window.game) {
                        window.game.addToLog(`🧪 Usas una Poción de Vida y recuperas ${healAmount} HP!`, "heal");
                    }
                    return true;

                case 'mana_potion':
                    const manaAmount = 30;
                    this.restoreMp(manaAmount);
                    if (window.game) {
                        window.game.addToLog(`🔮 Usas una Poción de Maná y recuperas ${manaAmount} MP!`, "heal");
                    }
                    return true;

                case 'super_potion':
                    const superHealAmount = 100;
                    this.heal(superHealAmount);
                    if (window.game) {
                        window.game.addToLog(`✨ Usas una Poción Superior y recuperas ${superHealAmount} HP!`, "heal");
                    }
                    return true;

                case 'gold_bag':
                    const goldAmount = Math.floor(50 + Math.random() * 100);
                    this.gold += goldAmount;
                    if (window.game) {
                        window.game.addToLog(`💰 Abres la Bolsa de Oro y obtienes ${goldAmount} oro!`, "heal");
                    }
                    return true;
            }
        }
        return false;
    }

    getSaveData() {
        return {
            level: this.level,
            exp: this.exp,
            expToNext: this.expToNext,
            maxHp: this.maxHp,
            hp: this.hp,
            maxMp: this.maxMp,
            mp: this.mp,
            atk: this.atk,
            def: this.def,
            spd: this.spd,
            currentFloor: this.currentFloor,
            gold: this.gold,
            inventory: { ...this.inventory },
            equipment: { ...this.equipment },
            equipmentDrops: this.equipmentDrops ? { ...this.equipmentDrops } : {},
            skills: { ...this.skills },
            currentSkill: this.currentSkill,
            shieldActive: this.shieldActive || 0,
            achievements: { ...this.achievements }
        };
    }

    loadSaveData(data) {
        Object.assign(this, data);
    }
}

class Enemy {
    constructor(name, avatar, hp, atk, def, floor, rarity = "común") {
        this.name = name;
        this.avatar = avatar;
        this.maxHp = hp;
        this.hp = hp;
        this.atk = atk;
        this.def = def;
        this.floor = floor;
        this.rarity = rarity;

        // Calcular recompensas basadas en el piso y rareza
        const rarityMultiplier = this.getRarityRewardMultiplier(rarity);
        const baseExp = 15 + (floor * 3);
        const baseGold = 8 + (floor * 2);

        this.expReward = Math.floor((baseExp + Math.random() * (baseExp * 0.5)) * rarityMultiplier);
        this.goldReward = Math.floor((baseGold + Math.random() * (baseGold * 0.3)) * rarityMultiplier);
    }

    getRarityRewardMultiplier(rarity) {
        const multipliers = {
            "común": 1.0,
            "poco común": 1.5,
            "raro": 2.0,
            "épico": 3.0,
            "legendario": 4.0,
            "jefe": 5.0
        };
        return multipliers[rarity] || 1.0;
    }

    takeDamage(damage) {
        this.hp = Math.max(0, this.hp - damage);
    }

    getRarityColor() {
        const colors = {
            "común": "#ffffff",
            "poco común": "#1eff00",
            "raro": "#0070dd",
            "épico": "#a335ee",
            "legendario": "#ff8000",
            "jefe": "#ff0000"
        };
        return colors[this.rarity] || "#ffffff";
    }
}

class Game {
    constructor() {
        this.player = new Player();
        this.currentEnemy = null;
        this.currentFloor = 1;
        this.inCombat = false;
        this.combatLog = [];
        
        this.initializeGame();
        this.bindEvents();
        this.startNewFloor();
    }

    initializeGame() {
        this.updateUI();
        this.updateInventoryDisplay();
        this.updateEquipmentDisplay();
        this.updateSkillButton();
        this.enableActionButtons(); // Asegurar que los botones estén habilitados
        this.addToLog("¡Bienvenido a la Torre Infinita! 🗼", "welcome");
        this.addToLog("Te encuentras en el primer piso. Un enemigo bloquea tu camino...", "info");
        this.addToLog("💡 Haz clic en los objetos de tu inventario para usarlos!", "info");
        this.addToLog("⚔️ Haz clic en equipamiento para equiparlo!", "info");
        this.addToLog("🔄 Clic derecho en el botón de habilidad para cambiar!", "info");
    }

    bindEvents() {
        // Botones de combate
        document.getElementById('attack-btn').addEventListener('click', () => this.playerAttack());
        document.getElementById('defend-btn').addEventListener('click', () => this.playerDefend());
        document.getElementById('skill-btn').addEventListener('click', () => this.playerSkill());
        document.getElementById('skill-btn').addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.cycleSkill();
        });
        document.getElementById('flee-btn').addEventListener('click', () => this.playerFlee());
        
        // Botones de exploración
        document.getElementById('next-floor-btn').addEventListener('click', () => this.nextFloor());
        document.getElementById('rest-btn').addEventListener('click', () => this.rest());
        document.getElementById('shop-btn').addEventListener('click', () => this.openShop());
        
        // Controles del footer
        document.getElementById('save-btn').addEventListener('click', () => this.saveGame());
        document.getElementById('load-btn').addEventListener('click', () => this.loadGame());

        // Eventos de inventario
        this.bindInventoryEvents();
    }

    startNewFloor() {
        this.currentFloor = this.player.currentFloor;
        this.currentEnemy = this.generateEnemy();
        this.inCombat = true;
        this.showCombatArea();
        this.enableActionButtons(); // Habilitar botones al empezar nuevo piso
        this.updateUI();
        this.updateSkillButton(); // Actualizar estado del botón de habilidad

        this.addToLog(`Piso ${this.currentFloor}: Te enfrentas a ${this.currentEnemy.name}!`, "info");
    }

    generateEnemy() {
        // Diferentes pools de enemigos según el piso
        let enemyPool;

        if (this.currentFloor <= 10) {
            enemyPool = [
                { name: "Goblin Salvaje", avatar: "👹", baseHp: 60, baseAtk: 8, baseDef: 3, rarity: "común" },
                { name: "Rata Gigante", avatar: "🐀", baseHp: 45, baseAtk: 6, baseDef: 2, rarity: "común" },
                { name: "Esqueleto Guerrero", avatar: "💀", baseHp: 80, baseAtk: 12, baseDef: 5, rarity: "poco común" },
                { name: "Araña Venenosa", avatar: "🕷️", baseHp: 55, baseAtk: 10, baseDef: 3, rarity: "común" }
            ];
        } else if (this.currentFloor <= 25) {
            enemyPool = [
                { name: "Orco Brutal", avatar: "👺", baseHp: 120, baseAtk: 15, baseDef: 8, rarity: "poco común" },
                { name: "Lobo Sombrio", avatar: "🐺", baseHp: 90, baseAtk: 14, baseDef: 6, rarity: "común" },
                { name: "Mago Oscuro", avatar: "🧙‍♂️", baseHp: 70, baseAtk: 18, baseDef: 4, rarity: "raro" },
                { name: "Golem de Piedra", avatar: "🗿", baseHp: 150, baseAtk: 12, baseDef: 12, rarity: "poco común" }
            ];
        } else if (this.currentFloor <= 50) {
            enemyPool = [
                { name: "Demonio Menor", avatar: "😈", baseHp: 180, baseAtk: 22, baseDef: 10, rarity: "raro" },
                { name: "Dragón Joven", avatar: "🐲", baseHp: 200, baseAtk: 25, baseDef: 15, rarity: "épico" },
                { name: "Lich Ancestral", avatar: "☠️", baseHp: 160, baseAtk: 28, baseDef: 8, rarity: "raro" },
                { name: "Minotauro", avatar: "🐂", baseHp: 220, baseAtk: 20, baseDef: 18, rarity: "poco común" }
            ];
        } else {
            enemyPool = [
                { name: "Señor Demonio", avatar: "👿", baseHp: 300, baseAtk: 35, baseDef: 20, rarity: "legendario" },
                { name: "Dragón Anciano", avatar: "🐉", baseHp: 400, baseAtk: 40, baseDef: 25, rarity: "legendario" },
                { name: "Avatar de la Muerte", avatar: "💀", baseHp: 350, baseAtk: 45, baseDef: 15, rarity: "épico" },
                { name: "Titán de Fuego", avatar: "🔥", baseHp: 500, baseAtk: 30, baseDef: 30, rarity: "legendario" }
            ];
        }

        // Jefe cada 10 pisos
        if (this.currentFloor % 10 === 0) {
            return this.generateBoss();
        }

        const type = enemyPool[Math.floor(Math.random() * enemyPool.length)];
        const floorMultiplier = 1 + (this.currentFloor - 1) * 0.15;
        const rarityMultiplier = this.getRarityMultiplier(type.rarity);

        return new Enemy(
            type.name,
            type.avatar,
            Math.floor(type.baseHp * floorMultiplier * rarityMultiplier),
            Math.floor(type.baseAtk * floorMultiplier * rarityMultiplier),
            Math.floor(type.baseDef * floorMultiplier * rarityMultiplier),
            this.currentFloor,
            type.rarity
        );
    }

    generateBoss() {
        const bossTypes = [
            { name: "Rey Goblin", avatar: "👑", baseHp: 200, baseAtk: 20, baseDef: 10 },
            { name: "Señor Esqueleto", avatar: "💀", baseHp: 250, baseAtk: 25, baseDef: 15 },
            { name: "Araña Reina", avatar: "🕸️", baseHp: 180, baseAtk: 30, baseDef: 8 },
            { name: "Dragón Guardian", avatar: "🐲", baseHp: 400, baseAtk: 35, baseDef: 20 }
        ];

        const bossIndex = Math.floor((this.currentFloor / 10) - 1) % bossTypes.length;
        const type = bossTypes[bossIndex];
        const floorMultiplier = 1 + (this.currentFloor - 1) * 0.2;

        return new Enemy(
            `${type.name} (JEFE)`,
            type.avatar,
            Math.floor(type.baseHp * floorMultiplier * 2),
            Math.floor(type.baseAtk * floorMultiplier * 1.5),
            Math.floor(type.baseDef * floorMultiplier * 1.5),
            this.currentFloor,
            "jefe"
        );
    }

    getRarityMultiplier(rarity) {
        const multipliers = {
            "común": 1.0,
            "poco común": 1.3,
            "raro": 1.6,
            "épico": 2.0,
            "legendario": 2.5,
            "jefe": 3.0
        };
        return multipliers[rarity] || 1.0;
    }

    playerAttack() {
        if (!this.inCombat || this.currentEnemy.hp <= 0) return;

        // Efectos visuales del botón
        const attackBtn = document.getElementById('attack-btn');
        attackBtn.classList.add('attacking');
        setTimeout(() => attackBtn.classList.remove('attacking'), 600);

        // Deshabilitar botones temporalmente
        this.disableActionButtons();

        const damage = this.calculateDamage(this.player.atk, this.currentEnemy.def);
        this.currentEnemy.takeDamage(damage);

        // Efectos visuales del enemigo
        const enemyAvatar = document.getElementById('enemy-avatar');
        enemyAvatar.classList.add('taking-damage');
        setTimeout(() => enemyAvatar.classList.remove('taking-damage'), 500);

        // Mostrar número de daño flotante
        this.showFloatingNumber(damage, 'damage', enemyAvatar);

        this.addToLog(`⚔️ Atacas a ${this.currentEnemy.name} por ${damage} de daño!`, "damage");

        setTimeout(() => {
            if (this.currentEnemy.hp <= 0) {
                this.enemyDefeated();
            } else {
                this.enemyTurn();
            }
        }, 1200);

        this.updateUI();
    }

    playerDefend() {
        if (!this.inCombat) return;

        // Efectos visuales del botón
        const defendBtn = document.getElementById('defend-btn');
        defendBtn.classList.add('defending');
        setTimeout(() => defendBtn.classList.remove('defending'), 600);

        this.disableActionButtons();

        this.player.defending = true;
        this.addToLog("🛡️ Te preparas para defenderte...", "info");

        setTimeout(() => this.enemyTurn(), 1200);
    }

    playerSkill() {
        if (!this.inCombat) return;

        const currentSkill = this.player.skills[this.player.currentSkill];

        // Verificar MP y cooldown
        if (this.player.mp < currentSkill.mpCost) {
            this.addToLog(`❌ No tienes suficiente MP! Necesitas ${currentSkill.mpCost} MP.`, "info");
            return;
        }

        if (currentSkill.cooldown > 0) {
            this.addToLog(`⏳ ${currentSkill.name} está en cooldown por ${currentSkill.cooldown} turnos!`, "info");
            return;
        }

        // Efectos visuales del botón
        const skillBtn = document.getElementById('skill-btn');
        skillBtn.classList.add('casting');
        setTimeout(() => skillBtn.classList.remove('casting'), 800);

        this.disableActionButtons();

        this.player.mp -= currentSkill.mpCost;
        currentSkill.cooldown = currentSkill.maxCooldown;

        this.executeSkill(this.player.currentSkill);

        setTimeout(() => {
            if (this.currentEnemy.hp <= 0) {
                this.enemyDefeated();
            } else {
                this.enemyTurn();
            }
        }, 1200);

        this.updateUI();
    }

    executeSkill(skillName) {
        const skill = this.player.skills[skillName];

        switch(skillName) {
            case 'magicStrike':
                const damage = this.calculateDamage(this.player.atk * 1.5, this.currentEnemy.def);
                this.currentEnemy.takeDamage(damage);

                const enemyAvatar = document.getElementById('enemy-avatar');
                enemyAvatar.classList.add('taking-damage');
                setTimeout(() => enemyAvatar.classList.remove('taking-damage'), 500);

                this.showFloatingNumber(damage, 'damage', enemyAvatar);
                this.addToLog(`✨ ¡Usas ${skill.name}! Infliges ${damage} de daño!`, "damage");
                break;

            case 'heal':
                const healAmount = Math.floor(this.player.maxHp * 0.4);
                this.player.heal(healAmount);

                const playerAvatar = document.querySelector('.player-avatar');
                this.showFloatingNumber(healAmount, 'heal', playerAvatar);
                this.addToLog(`💚 ¡Usas ${skill.name}! Recuperas ${healAmount} HP!`, "heal");
                break;

            case 'fireball':
                const fireballDamage = this.calculateDamage(this.player.atk * 2.2, this.currentEnemy.def);
                this.currentEnemy.takeDamage(fireballDamage);

                const enemyAvatarFire = document.getElementById('enemy-avatar');
                enemyAvatarFire.classList.add('taking-damage');
                setTimeout(() => enemyAvatarFire.classList.remove('taking-damage'), 500);

                this.showFloatingNumber(fireballDamage, 'damage', enemyAvatarFire);
                this.addToLog(`🔥 ¡Usas ${skill.name}! ¡Daño masivo de ${fireballDamage}!`, "damage");
                break;

            case 'shield':
                this.player.shieldActive = 3; // Dura 3 turnos
                this.addToLog(`🛡️ ¡Usas ${skill.name}! Daño reducido por 3 turnos!`, "heal");
                break;
        }
    }

    playerFlee() {
        if (!this.inCombat) return;
        
        const fleeChance = Math.random();
        if (fleeChance > 0.3) {
            this.addToLog("¡Escapas exitosamente!", "info");
            this.inCombat = false;
            this.showExplorationArea();
        } else {
            this.addToLog("¡No puedes escapar!", "info");
            setTimeout(() => this.enemyTurn(), 1000);
        }
    }

    enemyTurn() {
        if (!this.inCombat || this.currentEnemy.hp <= 0) return;

        let damage = this.calculateDamage(this.currentEnemy.atk, this.player.def, this.player.defending);

        // Aplicar efecto de escudo mágico
        if (this.player.shieldActive > 0) {
            damage = Math.floor(damage * 0.3); // Reduce daño al 30%
            this.player.shieldActive--;
            this.addToLog(`🛡️ ¡El escudo mágico reduce el daño! (${this.player.shieldActive} turnos restantes)`, "info");
        }

        this.player.takeDamage(damage);

        // Efectos visuales del jugador
        const playerAvatar = document.querySelector('.player-avatar');
        playerAvatar.classList.add('taking-damage');
        setTimeout(() => playerAvatar.classList.remove('taking-damage'), 500);

        // Mostrar número de daño flotante
        this.showFloatingNumber(damage, 'damage', playerAvatar);

        const defendText = this.player.defending ? " (¡Daño reducido por defensa!)" : "";
        this.player.defending = false;

        // Reducir cooldowns de habilidades
        this.player.reduceSkillCooldowns();

        this.addToLog(`👹 ${this.currentEnemy.name} te ataca por ${damage} de daño!${defendText}`, "damage");

        setTimeout(() => {
            if (this.player.hp <= 0) {
                this.gameOver();
            } else {
                this.enableActionButtons();
                this.updateSkillButton();
            }
        }, 1000);

        this.updateUI();
    }

    calculateDamage(atk, def, defending = false) {
        let damage = Math.max(1, atk - def + Math.floor(Math.random() * 5) - 2);
        if (defending) damage = Math.floor(damage * 0.5);
        return damage;
    }

    enemyDefeated() {
        this.inCombat = false;
        const expGained = this.currentEnemy.expReward;
        const goldGained = this.currentEnemy.goldReward;

        // Efectos visuales de victoria
        const enemyAvatar = document.getElementById('enemy-avatar');
        enemyAvatar.style.filter = 'grayscale(100%) brightness(0.5)';

        this.addToLog(`🎉 ¡${this.currentEnemy.name} derrotado!`, "heal");
        this.addToLog(`💰 Ganas ${expGained} EXP y ${goldGained} oro!`, "info");

        this.player.gainExp(expGained);
        this.player.gold += goldGained;

        // Actualizar logros
        this.player.achievements.enemiesKilled++;
        this.player.achievements.goldEarned += goldGained;

        if (this.currentEnemy.rarity === "jefe") {
            this.player.achievements.bossesKilled++;
        }

        this.checkAchievements();

        // Sistema de drops mejorado
        this.handleLootDrops();

        // Curación pequeña por victoria
        const healAmount = Math.floor(this.player.maxHp * 0.1);
        this.player.heal(healAmount);
        this.addToLog(`❤️ Recuperas ${healAmount} HP por la victoria!`, "heal");

        setTimeout(() => {
            this.showExplorationArea();
            this.updateUI();
            this.autoSave(); // Guardado automático después de cada victoria
        }, 2000);
    }

    handleLootDrops() {
        const dropChance = Math.random();

        // Drop de consumibles (40% chance)
        if (dropChance < 0.4) {
            const drops = [
                { item: "potion", name: "Poción de Vida", icon: "🧪", chance: 0.6 },
                { item: "mana_potion", name: "Poción de Maná", icon: "🔮", chance: 0.3 },
                { item: "gold_bag", name: "Bolsa de Oro", icon: "💰", chance: 0.1 }
            ];

            const randomDrop = Math.random();
            let cumulativeChance = 0;

            for (const drop of drops) {
                cumulativeChance += drop.chance;
                if (randomDrop <= cumulativeChance) {
                    this.player.addItem(drop.item, 1);
                    this.addToLog(`${drop.icon} ¡El enemigo dejó caer ${drop.name}!`, "heal");
                    break;
                }
            }
        }

        // Drop de equipamiento (15% chance, aumenta con rareza del enemigo)
        const equipmentChance = 0.15 * this.currentEnemy.getRarityRewardMultiplier(this.currentEnemy.rarity);
        if (Math.random() < equipmentChance) {
            this.generateEquipmentDrop();
        }

        // Drop especial cada 5 pisos
        if (this.currentFloor % 5 === 0 && Math.random() < 0.8) {
            this.addToLog("✨ ¡Drop especial de piso! Recibes una Poción Superior!", "heal");
            this.player.addItem("super_potion", 1);
        }

        // Drop de equipamiento garantizado de jefes
        if (this.currentEnemy.rarity === "jefe") {
            this.generateEquipmentDrop(true);
        }
    }

    generateEquipmentDrop(guaranteed = false) {
        const equipmentTypes = ['weapon', 'armor'];
        const type = equipmentTypes[Math.floor(Math.random() * equipmentTypes.length)];

        const equipment = this.generateRandomEquipment(type, this.currentFloor, guaranteed);

        // Añadir al inventario como item especial
        const equipmentKey = `${type}_${Date.now()}`;
        this.player.addItem(equipmentKey, 1);

        // Guardar datos del equipamiento
        if (!this.player.equipmentDrops) {
            this.player.equipmentDrops = {};
        }
        this.player.equipmentDrops[equipmentKey] = equipment;

        this.addToLog(`⚔️ ¡Equipamiento encontrado: ${equipment.name}!`, "heal");
        this.addToLog(`📊 ${equipment.statText}`, "info");
    }

    generateRandomEquipment(type, floor, isRare = false) {
        const weaponNames = [
            "Espada", "Hacha", "Martillo", "Daga", "Lanza", "Arco", "Bastón", "Maza"
        ];
        const armorNames = [
            "Armadura", "Cota de Malla", "Túnica", "Coraza", "Chaleco", "Robe", "Escudo"
        ];

        const materials = [
            { name: "de Hierro", multiplier: 1.0, rarity: "común" },
            { name: "de Acero", multiplier: 1.3, rarity: "poco común" },
            { name: "de Plata", multiplier: 1.6, rarity: "raro" },
            { name: "de Oro", multiplier: 2.0, rarity: "épico" },
            { name: "de Mithril", multiplier: 2.5, rarity: "legendario" }
        ];

        // Seleccionar material basado en el piso y si es raro
        let materialIndex = Math.min(Math.floor(floor / 10), materials.length - 1);
        if (isRare) materialIndex = Math.min(materialIndex + 1, materials.length - 1);

        const material = materials[materialIndex];
        const baseName = type === 'weapon' ?
            weaponNames[Math.floor(Math.random() * weaponNames.length)] :
            armorNames[Math.floor(Math.random() * armorNames.length)];

        const name = `${baseName} ${material.name}`;
        const floorMultiplier = 1 + (floor - 1) * 0.1;

        let stats = {};
        let statText = "";

        if (type === 'weapon') {
            const atkBonus = Math.floor((5 + floor * 0.5) * material.multiplier * floorMultiplier);
            stats.atk = atkBonus;
            statText = `ATK +${atkBonus}`;
        } else {
            const defBonus = Math.floor((3 + floor * 0.3) * material.multiplier * floorMultiplier);
            stats.def = defBonus;
            statText = `DEF +${defBonus}`;
        }

        return {
            name,
            type,
            stats,
            statText,
            rarity: material.rarity,
            floor
        };
    }

    nextFloor() {
        this.player.currentFloor++;

        // Eventos especiales cada ciertos pisos
        if (this.player.currentFloor % 25 === 0) {
            this.addToLog(`🎉 ¡Piso ${this.player.currentFloor}! Evento especial: ¡Curación completa!`, "heal");
            this.player.hp = this.player.maxHp;
            this.player.mp = this.player.maxMp;
        } else if (this.player.currentFloor % 15 === 0) {
            this.addToLog(`💰 ¡Piso ${this.player.currentFloor}! Evento especial: ¡Lluvia de oro!`, "heal");
            const bonusGold = this.player.currentFloor * 10;
            this.player.gold += bonusGold;
            this.addToLog(`Recibes ${bonusGold} oro extra!`, "info");
        }

        this.player.achievements.floorsCleared++;
        this.addToLog(`🔼 Subes al piso ${this.player.currentFloor}...`, "info");
        this.autoSave(); // Guardado automático al subir de piso
        this.startNewFloor();
    }

    openShop() {
        this.addToLog("🏪 Entras a la tienda misteriosa...", "info");
        this.addToLog("💰 Tienda - Precios:", "info");
        this.addToLog("🧪 Poción de Vida: 50 oro", "info");
        this.addToLog("🔮 Poción de Maná: 30 oro", "info");
        this.addToLog("✨ Poción Superior: 150 oro", "info");
        this.addToLog("💡 Escribe 'comprar pocion', 'comprar mana' o 'comprar super' en el chat para comprar!", "info");

        // Implementación básica de tienda
        this.showShopInterface();
    }

    showShopInterface() {
        // Por ahora, mostrar opciones de compra como botones temporales
        const shopButtons = document.createElement('div');
        shopButtons.className = 'shop-buttons';
        shopButtons.innerHTML = `
            <button class="action-btn secondary" onclick="game.buyItem('potion', 50)">🧪 Poción (50 oro)</button>
            <button class="action-btn secondary" onclick="game.buyItem('mana_potion', 30)">🔮 Maná (30 oro)</button>
            <button class="action-btn secondary" onclick="game.buyItem('super_potion', 150)">✨ Superior (150 oro)</button>
            <button class="action-btn danger" onclick="game.closeShop()">❌ Cerrar</button>
        `;

        const explorationArea = document.getElementById('exploration-area');
        explorationArea.appendChild(shopButtons);
    }

    buyItem(itemType, price) {
        if (this.player.gold >= price) {
            this.player.gold -= price;
            this.player.addItem(itemType, 1);
            this.addToLog(`✅ Compraste un item por ${price} oro!`, "heal");
            this.updateInventoryDisplay();
            this.updateUI();
        } else {
            this.addToLog(`❌ No tienes suficiente oro! Necesitas ${price} oro.`, "info");
        }
    }

    closeShop() {
        const shopButtons = document.querySelector('.shop-buttons');
        if (shopButtons) {
            shopButtons.remove();
        }
        this.addToLog("👋 Sales de la tienda.", "info");
    }

    rest() {
        const hpHealed = Math.floor(this.player.maxHp * 0.3);
        const mpRestored = Math.floor(this.player.maxMp * 0.5);
        
        this.player.heal(hpHealed);
        this.player.restoreMp(mpRestored);
        
        this.addToLog(`Descansas y recuperas ${hpHealed} HP y ${mpRestored} MP.`, "heal");
        this.updateUI();
    }

    showCombatArea() {
        document.getElementById('combat-area').classList.remove('hidden');
        document.getElementById('exploration-area').classList.add('hidden');
    }

    showExplorationArea() {
        document.getElementById('combat-area').classList.add('hidden');
        document.getElementById('exploration-area').classList.remove('hidden');
        document.getElementById('floor-number').textContent = this.currentFloor;
    }

    addToLog(message, type = "info") {
        const logElement = document.getElementById('combat-log');
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        entry.innerHTML = message;
        
        logElement.appendChild(entry);
        logElement.scrollTop = logElement.scrollHeight;
        
        // Limitar el número de entradas del log
        if (logElement.children.length > 20) {
            logElement.removeChild(logElement.firstChild);
        }
    }

    updateUI() {
        // Actualizar stats del jugador
        document.getElementById('player-level').textContent = this.player.level;
        document.getElementById('current-floor').textContent = this.player.currentFloor;
        document.getElementById('player-gold').textContent = this.player.gold;
        
        // Actualizar barras de vida/maná/experiencia
        this.updateBar('hp', this.player.hp, this.player.maxHp);
        this.updateBar('mp', this.player.mp, this.player.maxMp);
        this.updateBar('exp', this.player.exp, this.player.expToNext);
        
        // Actualizar stats detalladas
        document.getElementById('player-atk').textContent = this.player.atk;
        document.getElementById('player-def').textContent = this.player.def;
        document.getElementById('player-spd').textContent = this.player.spd;
        
        // Actualizar enemigo si está en combate
        if (this.currentEnemy && this.inCombat) {
            document.getElementById('enemy-avatar').textContent = this.currentEnemy.avatar;
            const enemyNameElement = document.getElementById('enemy-name');
            enemyNameElement.textContent = this.currentEnemy.name;
            enemyNameElement.style.color = this.currentEnemy.getRarityColor();
            this.updateEnemyBar();
        }
    }

    updateBar(type, current, max) {
        const fill = document.getElementById(`${type}-fill`);
        const text = document.getElementById(`${type}-text`);
        
        const percentage = (current / max) * 100;
        fill.style.width = `${percentage}%`;
        text.textContent = `${current}/${max}`;
    }

    updateEnemyBar() {
        const fill = document.getElementById('enemy-hp-fill');
        const text = document.getElementById('enemy-hp-text');
        
        const percentage = (this.currentEnemy.hp / this.currentEnemy.maxHp) * 100;
        fill.style.width = `${percentage}%`;
        text.textContent = `${this.currentEnemy.hp}/${this.currentEnemy.maxHp}`;
    }

    gameOver() {
        this.addToLog("💀 ¡Has sido derrotado! Game Over...", "damage");
        this.inCombat = false;
        this.disableActionButtons();

        // Efecto de game over
        const combatPanel = document.getElementById('combat-panel');
        combatPanel.classList.add('shake');
        setTimeout(() => combatPanel.classList.remove('shake'), 500);

        // Opción de reiniciar después de un tiempo
        setTimeout(() => {
            this.addToLog("🔄 Presiona F5 para reiniciar tu aventura...", "info");
        }, 2000);
    }

    showFloatingNumber(number, type, targetElement) {
        const floatingNumber = document.createElement('div');
        floatingNumber.className = `${type}-number`;
        floatingNumber.textContent = type === 'damage' ? `-${number}` : `+${number}`;

        // Posicionar relativo al elemento objetivo
        const rect = targetElement.getBoundingClientRect();
        floatingNumber.style.left = `${rect.left + rect.width / 2}px`;
        floatingNumber.style.top = `${rect.top}px`;

        document.body.appendChild(floatingNumber);

        // Remover después de la animación
        setTimeout(() => {
            if (floatingNumber.parentNode) {
                floatingNumber.parentNode.removeChild(floatingNumber);
            }
        }, 1500);
    }

    disableActionButtons() {
        const buttons = ['attack-btn', 'defend-btn', 'skill-btn', 'flee-btn'];
        buttons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = true;
        });
    }

    enableActionButtons() {
        const buttons = ['attack-btn', 'defend-btn', 'skill-btn', 'flee-btn'];
        buttons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = false;
        });
    }

    saveGame() {
        const saveData = {
            player: this.player.getSaveData(),
            currentFloor: this.currentFloor,
            timestamp: Date.now()
        };

        localStorage.setItem('torreInfinitaSave', JSON.stringify(saveData));
        this.addToLog("💾 Juego guardado exitosamente!", "heal");
    }

    autoSave() {
        // Guardado automático silencioso
        const saveData = {
            player: this.player.getSaveData(),
            currentFloor: this.currentFloor,
            timestamp: Date.now()
        };

        localStorage.setItem('torreInfinitaSave', JSON.stringify(saveData));
    }

    loadGame() {
        const saveData = localStorage.getItem('torreInfinitaSave');
        if (saveData) {
            const data = JSON.parse(saveData);
            this.player.loadSaveData(data.player);
            this.currentFloor = data.currentFloor;

            // Actualizar todas las interfaces
            this.updateUI();
            this.updateInventoryDisplay();
            this.updateEquipmentDisplay();
            this.updateSkillButton();
            this.enableActionButtons(); // Habilitar botones al cargar partida

            const saveDate = new Date(data.timestamp).toLocaleString();
            this.addToLog("📁 Juego cargado exitosamente!", "heal");
            this.addToLog(`⏰ Última partida: ${saveDate}`, "info");
            this.addToLog(`🗼 Piso ${this.currentFloor} - Nivel ${this.player.level}`, "info");
        } else {
            this.addToLog("❌ No se encontró ninguna partida guardada.", "info");
        }
    }

    showStatDistributionModal() {
        const modal = document.getElementById('level-up-modal');
        const newLevelSpan = document.getElementById('new-level');
        const statPointsSpan = document.getElementById('stat-points');

        newLevelSpan.textContent = this.player.level;

        // Inicializar sistema de distribución de puntos
        this.availableStatPoints = 3;
        this.tempStatBoosts = { atk: 0, def: 0, spd: 0 };

        statPointsSpan.textContent = this.availableStatPoints;

        // Resetear contadores visuales
        document.getElementById('atk-points').textContent = '0';
        document.getElementById('def-points').textContent = '0';
        document.getElementById('spd-points').textContent = '0';

        // Mostrar modal
        modal.classList.remove('hidden');

        // Bind eventos si no están ya bindeados
        if (!this.statModalBound) {
            this.bindStatModalEvents();
            this.statModalBound = true;
        }
    }

    bindStatModalEvents() {
        // Botones de distribución de stats
        document.querySelectorAll('.stat-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const stat = e.target.getAttribute('data-stat');
                this.addStatPoint(stat);
            });
        });

        // Botón de confirmar
        document.getElementById('confirm-stats').addEventListener('click', () => {
            this.confirmStatDistribution();
        });
    }

    addStatPoint(stat) {
        if (this.availableStatPoints > 0) {
            this.availableStatPoints--;
            this.tempStatBoosts[stat]++;

            // Actualizar UI
            document.getElementById('stat-points').textContent = this.availableStatPoints;
            document.getElementById(`${stat}-points`).textContent = this.tempStatBoosts[stat];

            // Deshabilitar botones si no hay más puntos
            if (this.availableStatPoints === 0) {
                document.querySelectorAll('.stat-btn').forEach(btn => {
                    btn.disabled = true;
                });
            }
        }
    }

    confirmStatDistribution() {
        // Aplicar las mejoras de stats
        this.player.atk += this.tempStatBoosts.atk * 2;
        this.player.def += this.tempStatBoosts.def * 2;
        this.player.spd += this.tempStatBoosts.spd * 2;

        // Cerrar modal
        document.getElementById('level-up-modal').classList.add('hidden');

        // Habilitar botones para la próxima vez
        document.querySelectorAll('.stat-btn').forEach(btn => {
            btn.disabled = false;
        });

        // Mostrar mejoras aplicadas
        if (this.tempStatBoosts.atk > 0) {
            this.addToLog(`⚔️ ATK aumentó en ${this.tempStatBoosts.atk * 2}!`, "heal");
        }
        if (this.tempStatBoosts.def > 0) {
            this.addToLog(`🛡️ DEF aumentó en ${this.tempStatBoosts.def * 2}!`, "heal");
        }
        if (this.tempStatBoosts.spd > 0) {
            this.addToLog(`💨 SPD aumentó en ${this.tempStatBoosts.spd * 2}!`, "heal");
        }

        this.updateUI();
    }

    bindInventoryEvents() {
        // Delegar eventos para items del inventario
        document.getElementById('inventory-list').addEventListener('click', (e) => {
            const item = e.target.closest('.item');
            if (item) {
                const itemType = item.getAttribute('data-item');
                this.useInventoryItem(itemType);
            }
        });
    }

    useInventoryItem(itemType) {
        if (!this.player.inventory[itemType] || this.player.inventory[itemType] <= 0) {
            this.addToLog("❌ No tienes ese objeto!", "info");
            return;
        }

        // Verificar si es equipamiento
        if (this.player.equipmentDrops && this.player.equipmentDrops[itemType]) {
            this.equipItem(itemType);
        } else {
            // Es un consumible
            const success = this.player.useItem(itemType);
            if (success) {
                this.updateInventoryDisplay();
                this.updateUI();
            }
        }
    }

    equipItem(equipmentKey) {
        const equipment = this.player.equipmentDrops[equipmentKey];
        const currentEquipment = this.player.equipment[equipment.type];

        // Remover stats del equipamiento actual
        if (currentEquipment && currentEquipment.stats) {
            for (const [stat, value] of Object.entries(currentEquipment.stats)) {
                this.player[stat] -= value;
            }
        }

        // Aplicar stats del nuevo equipamiento
        for (const [stat, value] of Object.entries(equipment.stats)) {
            this.player[stat] += value;
        }

        // Actualizar equipamiento
        this.player.equipment[equipment.type] = equipment;

        // Remover del inventario
        this.player.inventory[equipmentKey]--;
        if (this.player.inventory[equipmentKey] <= 0) {
            delete this.player.inventory[equipmentKey];
            delete this.player.equipmentDrops[equipmentKey];
        }

        this.addToLog(`⚔️ Equipas ${equipment.name}!`, "heal");
        this.addToLog(`📈 ${equipment.statText}`, "info");

        this.updateEquipmentDisplay();
        this.updateInventoryDisplay();
        this.updateUI();
    }

    updateEquipmentDisplay() {
        // Actualizar arma equipada
        const weaponElement = document.getElementById('equipped-weapon');
        if (this.player.equipment.weapon) {
            weaponElement.textContent = this.player.equipment.weapon.name;
            weaponElement.style.color = this.getRarityColor(this.player.equipment.weapon.rarity);
        }

        // Actualizar armadura equipada
        const armorElement = document.getElementById('equipped-armor');
        if (this.player.equipment.armor) {
            armorElement.textContent = this.player.equipment.armor.name;
            armorElement.style.color = this.getRarityColor(this.player.equipment.armor.rarity);
        }
    }

    updateInventoryDisplay() {
        const inventoryList = document.getElementById('inventory-list');
        inventoryList.innerHTML = '';

        const itemData = {
            potion: { icon: "🧪", name: "Poción de Vida", type: "consumable" },
            mana_potion: { icon: "🔮", name: "Poción de Maná", type: "consumable" },
            super_potion: { icon: "✨", name: "Poción Superior", type: "consumable" },
            gold_bag: { icon: "💰", name: "Bolsa de Oro", type: "consumable" }
        };

        // Mostrar consumibles
        for (const [itemType, quantity] of Object.entries(this.player.inventory)) {
            if (quantity > 0 && itemData[itemType]) {
                const itemElement = document.createElement('div');
                itemElement.className = 'item consumable-item';
                itemElement.setAttribute('data-item', itemType);
                itemElement.innerHTML = `
                    <span class="item-icon">${itemData[itemType].icon}</span>
                    <span class="item-name">${itemData[itemType].name}</span>
                    <span class="item-count">x${quantity}</span>
                `;
                inventoryList.appendChild(itemElement);
            }
        }

        // Mostrar equipamiento
        if (this.player.equipmentDrops) {
            for (const [equipmentKey, equipment] of Object.entries(this.player.equipmentDrops)) {
                if (this.player.inventory[equipmentKey] && this.player.inventory[equipmentKey] > 0) {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'item equipment-item';
                    itemElement.setAttribute('data-item', equipmentKey);
                    itemElement.style.borderLeft = `3px solid ${this.getRarityColor(equipment.rarity)}`;

                    const icon = equipment.type === 'weapon' ? '⚔️' : '🛡️';
                    itemElement.innerHTML = `
                        <span class="item-icon">${icon}</span>
                        <span class="item-name">${equipment.name}</span>
                        <span class="item-stats">${equipment.statText}</span>
                    `;
                    inventoryList.appendChild(itemElement);
                }
            }
        }
    }

    getRarityColor(rarity) {
        const colors = {
            "común": "#ffffff",
            "poco común": "#1eff00",
            "raro": "#0070dd",
            "épico": "#a335ee",
            "legendario": "#ff8000"
        };
        return colors[rarity] || "#ffffff";
    }

    updateSkillButton() {
        const skillBtn = document.getElementById('skill-btn');
        const currentSkill = this.player.skills[this.player.currentSkill];

        if (currentSkill.cooldown > 0) {
            skillBtn.textContent = `⏳ ${currentSkill.name} (${currentSkill.cooldown})`;
            skillBtn.disabled = true;
        } else if (this.player.mp < currentSkill.mpCost) {
            skillBtn.textContent = `❌ ${currentSkill.name} (${currentSkill.mpCost} MP)`;
            skillBtn.disabled = true;
        } else {
            skillBtn.textContent = `✨ ${currentSkill.name}`;
            skillBtn.disabled = false;
        }
    }

    cycleSkill() {
        const skillKeys = Object.keys(this.player.skills).filter(key =>
            this.player.skills[key].unlocked !== false
        );

        const currentIndex = skillKeys.indexOf(this.player.currentSkill);
        const nextIndex = (currentIndex + 1) % skillKeys.length;
        this.player.currentSkill = skillKeys[nextIndex];

        this.updateSkillButton();

        const skill = this.player.skills[this.player.currentSkill];
        this.addToLog(`🔄 Habilidad seleccionada: ${skill.name} (${skill.mpCost} MP)`, "info");
    }

    checkAchievements() {
        const achievements = this.player.achievements;

        // Logros de enemigos derrotados
        if (achievements.enemiesKilled === 10) {
            this.addToLog("🏆 ¡Logro desbloqueado: Cazador Novato! (10 enemigos)", "heal");
            this.player.gold += 100;
        } else if (achievements.enemiesKilled === 50) {
            this.addToLog("🏆 ¡Logro desbloqueado: Guerrero Veterano! (50 enemigos)", "heal");
            this.player.gold += 500;
        } else if (achievements.enemiesKilled === 100) {
            this.addToLog("🏆 ¡Logro desbloqueado: Leyenda de la Torre! (100 enemigos)", "heal");
            this.player.gold += 1000;
        }

        // Logros de pisos
        if (achievements.floorsCleared === 10) {
            this.addToLog("🏆 ¡Logro desbloqueado: Explorador! (10 pisos)", "heal");
            this.player.addItem("super_potion", 3);
        } else if (achievements.floorsCleared === 25) {
            this.addToLog("🏆 ¡Logro desbloqueado: Maestro de la Torre! (25 pisos)", "heal");
            this.player.addItem("super_potion", 5);
        }

        // Logros de jefes
        if (achievements.bossesKilled === 1) {
            this.addToLog("🏆 ¡Logro desbloqueado: Matajefes! (1 jefe)", "heal");
            this.player.gold += 200;
        } else if (achievements.bossesKilled === 5) {
            this.addToLog("🏆 ¡Logro desbloqueado: Némesis de Jefes! (5 jefes)", "heal");
            this.player.gold += 1000;
        }

        // Logros de oro
        if (achievements.goldEarned >= 1000) {
            this.addToLog("🏆 ¡Logro desbloqueado: Acumulador de Riquezas! (1000 oro)", "heal");
        }
    }
}

// Inicializar el juego cuando se carga la página
document.addEventListener('DOMContentLoaded', () => {
    window.game = new Game();
});
