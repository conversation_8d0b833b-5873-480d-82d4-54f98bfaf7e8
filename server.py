#!/usr/bin/env python3
"""
Servidor simple para el juego Torre Infinita
Ejecutar con: python server.py
"""

import http.server
import socketserver
import webbrowser
import os
import sys

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def start_server():
    # Cambiar al directorio del script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🗼 Torre Infinita - Servidor iniciado en puerto {PORT}")
        print(f"🌐 Abre tu navegador en: http://localhost:{PORT}")
        print("🛑 Presiona Ctrl+C para detener el servidor")
        
        # Abrir automáticamente el navegador
        try:
            webbrowser.open(f'http://localhost:{PORT}')
        except:
            pass
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Servidor detenido")
            sys.exit(0)

if __name__ == "__main__":
    start_server()
