/* Reset y configuración base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto Mono', monospace;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #e0e0e0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* <PERSON><PERSON> Principal */
.menu-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    z-index: 1000;
}

.menu-content {
    text-align: center;
    max-width: 600px;
    padding: 40px;
    background: rgba(0, 0, 0, 0.8);
    border: 3px solid #4a9eff;
    border-radius: 20px;
    box-shadow: 0 0 40px rgba(74, 158, 255, 0.5);
}

.main-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    color: #4a9eff;
    text-shadow: 0 0 20px rgba(74, 158, 255, 0.8);
    margin-bottom: 10px;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { text-shadow: 0 0 20px rgba(74, 158, 255, 0.8); }
    to { text-shadow: 0 0 30px rgba(74, 158, 255, 1), 0 0 40px rgba(74, 158, 255, 0.6); }
}

.subtitle {
    font-size: 1.2rem;
    color: #888;
    margin-bottom: 40px;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
}

.menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-family: 'Roboto Mono', monospace;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.menu-btn.primary {
    background: linear-gradient(135deg, #4a9eff, #357abd);
    color: white;
    box-shadow: 0 4px 15px rgba(74, 158, 255, 0.3);
}

.menu-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(74, 158, 255, 0.5);
}

.menu-btn.secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.menu-btn.secondary:hover {
    transform: translateY(-3px);
    background: linear-gradient(135deg, #7c858d, #596067);
}

.menu-btn.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.menu-btn.danger:hover {
    transform: translateY(-3px);
    background: linear-gradient(135deg, #ec4555, #d83343);
}

.game-info {
    border-top: 1px solid #333;
    padding-top: 20px;
    font-size: 0.9rem;
    color: #aaa;
}

.game-info p {
    margin: 5px 0;
}

/* Creación de Personaje */
.creation-content {
    text-align: center;
    max-width: 800px;
    padding: 30px;
    background: rgba(0, 0, 0, 0.9);
    border: 3px solid #4a9eff;
    border-radius: 20px;
    box-shadow: 0 0 40px rgba(74, 158, 255, 0.5);
}

.creation-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    color: #4a9eff;
    margin-bottom: 30px;
    text-shadow: 0 0 15px rgba(74, 158, 255, 0.6);
}

.character-form {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.form-section {
    text-align: left;
}

.form-section label {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #4a9eff;
    margin-bottom: 15px;
}

#character-name {
    width: 100%;
    padding: 12px;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid #333;
    border-radius: 8px;
    color: #e0e0e0;
    font-family: 'Roboto Mono', monospace;
}

#character-name:focus {
    outline: none;
    border-color: #4a9eff;
    box-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
}

/* Selección de apariencia */
.appearance-selection {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.appearance-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 3px solid transparent;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.appearance-option:hover {
    border-color: #4a9eff;
    background: rgba(74, 158, 255, 0.1);
    transform: translateY(-5px);
}

.appearance-option.selected {
    border-color: #4a9eff;
    background: rgba(74, 158, 255, 0.2);
    box-shadow: 0 0 20px rgba(74, 158, 255, 0.4);
}

.appearance-option img {
    width: 80px;
    height: 80px;
    margin-bottom: 10px;
    border-radius: 10px;
}

.appearance-option span {
    font-weight: bold;
    color: #e0e0e0;
}

/* Distribución de estadísticas */
.stats-distribution {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-allocation {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #333;
}

.stat-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-name {
    font-weight: bold;
    color: #e0e0e0;
    cursor: help;
    flex: 1;
}

.stat-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stat-decrease, .stat-increase {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background: #4a9eff;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.stat-decrease:hover, .stat-increase:hover {
    background: #357abd;
    transform: scale(1.1);
}

.stat-decrease:disabled, .stat-increase:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4a9eff;
    min-width: 30px;
    text-align: center;
}

.points-remaining {
    text-align: center;
    font-size: 1.1rem;
    font-weight: bold;
    color: #4a9eff;
    margin-top: 15px;
    padding: 10px;
    background: rgba(74, 158, 255, 0.1);
    border-radius: 8px;
}

.creation-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

/* Contenedor principal */
#game-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    padding: 10px;
}

/* Header */
#player-header {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #4a9eff;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 0 20px rgba(74, 158, 255, 0.3);
}

.player-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.game-title {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 900;
    color: #4a9eff;
    text-shadow: 0 0 10px rgba(74, 158, 255, 0.5);
}

.player-stats {
    display: flex;
    gap: 30px;
}

.stat-group {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.8rem;
    color: #888;
    margin-bottom: 2px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4a9eff;
}

/* Panel principal */
#game-main {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 15px;
    flex: 1;
    margin-bottom: 10px;
}

.game-panel {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #333;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.main-panel {
    border-color: #4a9eff;
    box-shadow: 0 0 20px rgba(74, 158, 255, 0.2);
}

/* Panel del jugador */
#player-panel h2 {
    color: #4a9eff;
    margin-bottom: 15px;
    text-align: center;
    font-family: 'Orbitron', monospace;
}

.player-avatar {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 10px;
}

.player-name {
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 20px;
    color: #4a9eff;
}

/* Barras de estadísticas */
.stats-container {
    margin-bottom: 20px;
}

.stat-bar {
    margin-bottom: 15px;
}

.stat-bar label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #ccc;
}

.bar-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar {
    flex: 1;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #333;
}

.bar-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.hp-bar .bar-fill {
    background: linear-gradient(90deg, #ff4444, #ff6666);
}

.mp-bar .bar-fill {
    background: linear-gradient(90deg, #4444ff, #6666ff);
}

.exp-bar .bar-fill {
    background: linear-gradient(90deg, #44ff44, #66ff66);
}

.bar-text {
    font-size: 0.9rem;
    font-weight: bold;
    min-width: 60px;
    text-align: right;
}

/* Estadísticas detalladas */
.detailed-stats {
    border-top: 1px solid #333;
    padding-top: 15px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
}

/* Panel de combate */
#combat-panel {
    display: flex;
    flex-direction: column;
}

#combat-area {
    flex: 1;
}

.combatant-section {
    text-align: center;
    margin-bottom: 20px;
}

.enemy-avatar {
    font-size: 4rem;
    margin-bottom: 10px;
}

.enemy-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #ff6b6b;
    margin-bottom: 10px;
}

.enemy-hp-bar {
    max-width: 300px;
    margin: 0 auto;
}

/* Log de combate */
.combat-log {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #333;
    border-radius: 8px;
    padding: 15px;
    height: 200px;
    overflow-y: auto;
    margin: 20px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 5px;
    animation: fadeIn 0.5s ease;
}

.log-entry.welcome {
    background: rgba(74, 158, 255, 0.2);
    border-left: 3px solid #4a9eff;
}

.log-entry.damage {
    background: rgba(255, 68, 68, 0.2);
    border-left: 3px solid #ff4444;
}

.log-entry.heal {
    background: rgba(68, 255, 68, 0.2);
    border-left: 3px solid #44ff44;
}

.log-entry.info {
    background: rgba(255, 255, 68, 0.2);
    border-left: 3px solid #ffff44;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Botones de acción */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 20px;
}

.action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-family: 'Roboto Mono', monospace;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #4a9eff, #357abd);
    color: white;
    box-shadow: 0 4px 15px rgba(74, 158, 255, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 158, 255, 0.4);
}

.action-btn.secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.action-btn.magic {
    background: linear-gradient(135deg, #9c27b0, #7b1fa2);
    color: white;
}

.action-btn.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    filter: brightness(1.1);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Panel de inventario */
#inventory-panel h2, #inventory-panel h3 {
    color: #4a9eff;
    margin-bottom: 15px;
    font-family: 'Orbitron', monospace;
}

.equipment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
}

.equipment-slot {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid #333;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    transition: border-color 0.3s ease;
}

.equipment-slot:hover {
    border-color: #4a9eff;
}

.slot-icon {
    font-size: 2rem;
    margin-bottom: 5px;
}

.slot-name {
    font-size: 0.8rem;
    color: #888;
    margin-bottom: 5px;
}

.equipped-item {
    font-size: 0.9rem;
    font-weight: bold;
    color: #4a9eff;
}

.item-list {
    max-height: 300px;
    overflow-y: auto;
}

.item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    margin-bottom: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.item:hover {
    background: rgba(74, 158, 255, 0.2);
}

.item-icon {
    font-size: 1.5rem;
}

.item-name {
    flex: 1;
}

.item-count {
    color: #888;
    font-size: 0.9rem;
}

.item-stats {
    color: #4a9eff;
    font-size: 0.8rem;
    font-weight: bold;
}

.equipment-item {
    border-left: 3px solid transparent;
    background: linear-gradient(90deg, rgba(74, 158, 255, 0.1), rgba(0, 0, 0, 0.05));
}

.equipment-item:hover {
    background: linear-gradient(90deg, rgba(74, 158, 255, 0.3), rgba(0, 0, 0, 0.1));
    transform: translateX(5px);
}

.consumable-item:hover {
    background: rgba(68, 255, 68, 0.2);
}

/* Footer */
#game-footer {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #333;
    border-radius: 10px;
    padding: 10px;
}

.footer-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.control-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #333;
    border-radius: 5px;
    color: #e0e0e0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(74, 158, 255, 0.3);
    border-color: #4a9eff;
}

/* Modales */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 3px solid #4a9eff;
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 0 30px rgba(74, 158, 255, 0.5);
    animation: modalAppear 0.3s ease;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-content h2 {
    color: #4a9eff;
    font-family: 'Orbitron', monospace;
    margin-bottom: 20px;
    font-size: 2rem;
    text-shadow: 0 0 10px rgba(74, 158, 255, 0.5);
}

.stat-distribution {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #333;
}

.stat-btn {
    background: #4a9eff;
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.stat-btn:hover {
    background: #357abd;
    transform: scale(1.1);
}

.stat-btn:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

/* Efectos de combate */
.damage-number {
    position: absolute;
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff4444;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    pointer-events: none;
    animation: damageFloat 1.5s ease-out forwards;
    z-index: 100;
}

.heal-number {
    position: absolute;
    font-size: 1.5rem;
    font-weight: bold;
    color: #44ff44;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    pointer-events: none;
    animation: damageFloat 1.5s ease-out forwards;
    z-index: 100;
}

@keyframes damageFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-30px) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.8);
    }
}

/* Efectos de botones */
.action-btn.attacking {
    animation: attackPulse 0.6s ease;
}

@keyframes attackPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(255, 68, 68, 0.6); }
}

.action-btn.defending {
    animation: defendGlow 0.6s ease;
}

@keyframes defendGlow {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(68, 68, 255, 0.6); }
}

.action-btn.casting {
    animation: castSparkle 0.8s ease;
}

@keyframes castSparkle {
    0%, 100% { transform: scale(1); }
    25% { transform: scale(1.1) rotate(2deg); box-shadow: 0 0 25px rgba(156, 39, 176, 0.8); }
    75% { transform: scale(1.1) rotate(-2deg); box-shadow: 0 0 25px rgba(156, 39, 176, 0.8); }
}

/* Efectos de avatares */
.enemy-avatar.taking-damage {
    animation: enemyHit 0.5s ease;
}

@keyframes enemyHit {
    0%, 100% { transform: translateX(0); filter: brightness(1); }
    25% { transform: translateX(-10px); filter: brightness(1.5) hue-rotate(0deg); }
    75% { transform: translateX(10px); filter: brightness(1.5) hue-rotate(180deg); }
}

.player-avatar.taking-damage {
    animation: playerHit 0.5s ease;
}

@keyframes playerHit {
    0%, 100% { transform: translateX(0); filter: brightness(1); }
    25% { transform: translateX(10px); filter: brightness(1.5) hue-rotate(0deg); }
    75% { transform: translateX(-10px); filter: brightness(1.5) hue-rotate(180deg); }
}

/* Utilidades */
.hidden {
    display: none !important;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Responsive */
@media (max-width: 1200px) {
    #game-main {
        grid-template-columns: 250px 1fr 250px;
    }
}

@media (max-width: 900px) {
    #game-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .player-stats {
        gap: 15px;
    }
}
